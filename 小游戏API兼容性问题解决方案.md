# 小游戏 API 兼容性问题解决方案

## 问题描述

在将 uni-app 小程序转换为小游戏时，可能会遇到以下错误：

```
TypeError: wx$2.canIUse is not a function
```

这是因为小游戏环境中没有 `wx.canIUse` 方法，但 uni-app 框架尝试使用它来检查 API 可用性。

## 解决方案

### 方案一：使用更新的转换脚本（推荐）

1. **重新编译项目**
   ```
   在 HBuilderX 中：运行 -> 运行到小程序模拟器 -> 微信开发者工具
   ```

2. **运行更新的转换脚本**
   ```
   双击运行 "转换为小游戏.bat"
   ```
   
   脚本会自动：
   - 创建 `game.json` 配置文件
   - 创建带 API 兼容性补丁的 `game.js` 文件

3. **在微信开发者工具中导入**
   - 选择"小游戏"项目类型
   - 目录：`unpackage\dist\dev\mp-weixin`

### 方案二：手动修复

如果自动脚本不工作，可以手动修复：

1. **在编译目录中找到 `game.js` 文件**
   ```
   unpackage\dist\dev\mp-weixin\game.js
   ```

2. **在文件开头添加兼容性补丁**
   ```javascript
   // 小游戏 API 兼容性补丁
   if (typeof wx !== 'undefined' && !wx.canIUse) {
     wx.canIUse = function(apiName) {
       const gameAPIs = [
         'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 
         'getWindowInfo', 'getDeviceInfo', 'request', 'connectSocket'
       ];
       if (gameAPIs.includes(apiName)) {
         return typeof wx[apiName] === 'function';
       }
       return typeof wx[apiName] !== 'undefined';
     };
     
     if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
       wx.getAppBaseInfo = wx.getSystemInfoSync;
     }
     if (!wx.getWindowInfo && wx.getSystemInfoSync) {
       wx.getWindowInfo = wx.getSystemInfoSync;
     }
     if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
       wx.getDeviceInfo = wx.getSystemInfoSync;
     }
   }
   ```

## 验证修复

修复后，在微信开发者工具的控制台中应该看到：

```
应用小游戏 API 兼容性补丁
小游戏 API 补丁应用完成
小游戏启动成功
```

## 常见问题

### Q: 为什么小游戏没有 `wx.canIUse` 方法？
A: 小游戏和小程序是不同的运行环境，API 有所差异。小游戏环境更接近游戏引擎，某些小程序特有的 API 不可用。

### Q: 这个补丁会影响功能吗？
A: 不会。补丁只是提供了 `wx.canIUse` 方法的实现，确保 uni-app 框架能正常检查 API 可用性。

### Q: 每次编译都需要重新应用补丁吗？
A: 是的。因为编译会重新生成 `game.js` 文件，所以需要重新应用补丁。建议使用自动化脚本。

## 自动化解决方案

为了避免每次都手动修复，项目中已包含：

1. **转换脚本** (`转换为小游戏.bat`) - 自动应用补丁
2. **源码补丁** (`game.js`) - 在源码级别包含补丁
3. **API 补丁文件** (`小游戏API补丁.js`) - 独立的补丁文件

建议使用转换脚本，它会自动处理所有兼容性问题。

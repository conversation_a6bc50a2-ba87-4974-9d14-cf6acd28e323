# 验证小游戏兼容性补丁脚本

$sourceDir = "unpackage\dist\dev\mp-weixin"

Write-Host "========================================" -ForegroundColor Green
Write-Host "        小游戏补丁验证工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查编译目录
if (-not (Test-Path $sourceDir)) {
    Write-Host "❌ 编译目录不存在：$sourceDir" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 编译目录存在" -ForegroundColor Green

# 检查 game.json
if (Test-Path "$sourceDir\game.json") {
    Write-Host "✅ game.json 文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ game.json 文件不存在" -ForegroundColor Red
}

# 检查 game.js
if (Test-Path "$sourceDir\game.js") {
    Write-Host "✅ game.js 文件存在" -ForegroundColor Green
    
    # 检查补丁内容
    $gameContent = Get-Content "$sourceDir\game.js" -Raw
    
    if ($gameContent -match "小游戏兼容性补丁") {
        Write-Host "✅ game.js 包含兼容性补丁" -ForegroundColor Green
    } else {
        Write-Host "❌ game.js 不包含兼容性补丁" -ForegroundColor Red
    }
    
    if ($gameContent -match "wx\.canIUse") {
        Write-Host "✅ game.js 包含 wx.canIUse 补丁" -ForegroundColor Green
    } else {
        Write-Host "❌ game.js 不包含 wx.canIUse 补丁" -ForegroundColor Red
    }
    
    if ($gameContent -match "global") {
        Write-Host "✅ game.js 包含 global 对象补丁" -ForegroundColor Green
    } else {
        Write-Host "❌ game.js 不包含 global 对象补丁" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ game.js 文件不存在" -ForegroundColor Red
}

# 检查 app.js
if (Test-Path "$sourceDir\app.js") {
    Write-Host "✅ app.js 文件存在" -ForegroundColor Green
    
    $appContent = Get-Content "$sourceDir\app.js" -Raw
    
    if ($appContent -match "小游戏兼容性补丁") {
        Write-Host "✅ app.js 包含兼容性补丁" -ForegroundColor Green
    } else {
        Write-Host "❌ app.js 不包含兼容性补丁" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ app.js 文件不存在" -ForegroundColor Red
}

# 检查 vendor.js 中的问题代码
if (Test-Path "$sourceDir\common\vendor.js") {
    Write-Host "✅ vendor.js 文件存在" -ForegroundColor Green
    
    $vendorContent = Get-Content "$sourceDir\common\vendor.js" -Raw
    
    if ($vendorContent -match "wx\$2\.canIUse") {
        Write-Host "⚠️  vendor.js 包含 wx`$2.canIUse 调用 - 需要补丁" -ForegroundColor Yellow
    } else {
        Write-Host "✅ vendor.js 不包含问题代码" -ForegroundColor Green
    }
    
} else {
    Write-Host "❌ vendor.js 文件不存在" -ForegroundColor Red
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "验证完成" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host "`n如果所有检查都通过，可以在微信开发者工具中：" -ForegroundColor White
Write-Host "1. 选择'小游戏'项目类型" -ForegroundColor White
Write-Host "2. 导入目录：$(Get-Location)\$sourceDir" -ForegroundColor White
Write-Host "3. 查看控制台是否有'应用小游戏兼容性补丁'日志" -ForegroundColor White

Read-Host "`n按任意键退出"

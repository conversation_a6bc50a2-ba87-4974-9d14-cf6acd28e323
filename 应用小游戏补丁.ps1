# 小游戏兼容性补丁应用脚本

$sourceDir = "unpackage\dist\dev\mp-weixin"

Write-Host "========================================" -ForegroundColor Green
Write-Host "        小游戏兼容性补丁应用工具" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查编译目录是否存在
if (-not (Test-Path $sourceDir)) {
    Write-Host "错误：编译目录不存在！" -ForegroundColor Red
    Write-Host "请先通过 HBuilderX 编译项目" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

# 创建 game.json
Write-Host "创建 game.json..." -ForegroundColor Yellow
$gameJsonContent = @"
{
  "deviceOrientation": "portrait",
  "showStatusBar": false,
  "networkTimeout": {
    "request": 60000,
    "connectSocket": 60000,
    "uploadFile": 60000,
    "downloadFile": 60000
  },
  "subpackages": [],
  "plugins": {},
  "preloadRule": {},
  "resizable": false
}
"@
$gameJsonContent | Out-File -FilePath "$sourceDir\game.json" -Encoding UTF8

# 兼容性补丁代码
$patchCode = @"
// 小游戏兼容性补丁 - 必须在 vendor.js 之前执行
(function() {
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  if (typeof global === 'undefined') {
    globalObj.global = globalObj;
  }

  if (typeof wx !== 'undefined') {
    console.log('应用小游戏兼容性补丁');

    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = [
          'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
          'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting'
        ];
        return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';
      };
    }

    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
      wx.getAppBaseInfo = wx.getSystemInfoSync;
    }
    if (!wx.getWindowInfo && wx.getSystemInfoSync) {
      wx.getWindowInfo = wx.getSystemInfoSync;
    }
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
      wx.getDeviceInfo = wx.getSystemInfoSync;
    }

    if (typeof Page === 'undefined') {
      globalObj.Page = function(options) { return options; };
    }
    if (typeof Component === 'undefined') {
      globalObj.Component = function(options) { return options; };
    }
    if (typeof App === 'undefined') {
      globalObj.App = function(options) { return options; };
    }
    if (typeof getApp === 'undefined') {
      globalObj.getApp = function(options) { return { `$vm: null, globalData: {} }; };
    }

    console.log('小游戏兼容性补丁应用完成');
  }
})();

"@

# 应用补丁到 app.js
if (Test-Path "$sourceDir\app.js") {
    Write-Host "应用补丁到 app.js..." -ForegroundColor Yellow
    
    $appContent = Get-Content "$sourceDir\app.js" -Raw
    
    # 在 vendor.js 引用之前插入补丁
    $patchedContent = $appContent -replace '("use strict";)', "`$1`n`n$patchCode"
    
    $patchedContent | Out-File -FilePath "$sourceDir\app.js" -Encoding UTF8 -NoNewline
    
    # 复制到 game.js
    Copy-Item "$sourceDir\app.js" "$sourceDir\game.js"
    
    Write-Host "✓ 补丁已应用到 app.js 和 game.js" -ForegroundColor Green
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "补丁应用完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "`n现在可以在微信开发者工具中：" -ForegroundColor White
Write-Host "1. 选择'小游戏'项目类型" -ForegroundColor White
Write-Host "2. 导入目录：$(Get-Location)\$sourceDir" -ForegroundColor White
Write-Host "3. 填入您的小游戏 AppID" -ForegroundColor White

Read-Host "`n按任意键退出"

@echo off
echo Converting to Mini Game...

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo Error: Build directory not found!
    echo Please build the project first using H<PERSON>uilderX
    pause
    exit /b 1
)

echo Creating game.json...
echo {> "%SOURCE_DIR%\game.json"
echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
echo   },>> "%SOURCE_DIR%\game.json"
echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
echo   "resizable": false>> "%SOURCE_DIR%\game.json"
echo }>> "%SOURCE_DIR%\game.json"

if exist "%SOURCE_DIR%\app.js" (
    echo Creating game.js with API compatibility patch...

    echo "use strict";> "%SOURCE_DIR%\game.js"
    echo.>> "%SOURCE_DIR%\game.js"
    echo // Mini-game API compatibility patch>> "%SOURCE_DIR%\game.js"
    echo (function() {>> "%SOURCE_DIR%\game.js"
    echo   if (typeof wx !== 'undefined') {>> "%SOURCE_DIR%\game.js"
    echo     console.log('Applying mini-game API compatibility patch');>> "%SOURCE_DIR%\game.js"
    echo     if (!wx.canIUse) {>> "%SOURCE_DIR%\game.js"
    echo       wx.canIUse = function(apiName) {>> "%SOURCE_DIR%\game.js"
    echo         const gameAPIs = ['getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo'];>> "%SOURCE_DIR%\game.js"
    echo         return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';>> "%SOURCE_DIR%\game.js"
    echo       };>> "%SOURCE_DIR%\game.js"
    echo     }>> "%SOURCE_DIR%\game.js"
    echo     if (!wx.getAppBaseInfo ^&^& wx.getSystemInfoSync) wx.getAppBaseInfo = wx.getSystemInfoSync;>> "%SOURCE_DIR%\game.js"
    echo     const globalObj = (typeof globalThis !== 'undefined') ? globalThis : this;>> "%SOURCE_DIR%\game.js"
    echo     if (typeof Page === 'undefined') globalObj.Page = function(options) { return options; };>> "%SOURCE_DIR%\game.js"
    echo     if (typeof Component === 'undefined') globalObj.Component = function(options) { return options; };>> "%SOURCE_DIR%\game.js"
    echo     if (typeof App === 'undefined') globalObj.App = function(options) { return options; };>> "%SOURCE_DIR%\game.js"
    echo     if (typeof getApp === 'undefined') globalObj.getApp = function() { return {$vm:null,globalData:{}}; };>> "%SOURCE_DIR%\game.js"
    echo     console.log('Mini-game API patch applied');>> "%SOURCE_DIR%\game.js"
    echo   }>> "%SOURCE_DIR%\game.js"
    echo })();>> "%SOURCE_DIR%\game.js"
    echo.>> "%SOURCE_DIR%\game.js"

    more +2 "%SOURCE_DIR%\app.js" >> "%SOURCE_DIR%\game.js"
    echo game.js created with API patch
)

echo.
echo Conversion completed!
echo Import directory: %cd%\%SOURCE_DIR%
echo.
pause

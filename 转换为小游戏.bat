@echo off
echo Converting to Mini Game...

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%SOURCE_DIR%" (
    echo Error: Build directory not found!
    echo Please build the project first using H<PERSON>uilderX
    pause
    exit /b 1
)

echo Creating game.json...
echo {> "%SOURCE_DIR%\game.json"
echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
echo   },>> "%SOURCE_DIR%\game.json"
echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
echo   "resizable": false>> "%SOURCE_DIR%\game.json"
echo }>> "%SOURCE_DIR%\game.json"

if exist "%SOURCE_DIR%\app.js" (
    copy "%SOURCE_DIR%\app.js" "%SOURCE_DIR%\game.js" >nul
    echo game.js created (API patches are now in source code)
)

echo.
echo Conversion completed!
echo Import directory: %cd%\%SOURCE_DIR%
echo.
pause

@echo off
chcp 65001
echo ========================================
echo        仗剑江湖行 - 小游戏转换工具
echo ========================================
echo.

set "SOURCE_DIR=unpackage\dist\dev\mp-weixin"
set "GAME_JSON_SOURCE=game.json"

echo 正在检查编译目录...
if not exist "%SOURCE_DIR%" (
    echo 错误：编译目录不存在！
    echo 请先通过 HBuilderX 编译项目
    echo 运行 -> 运行到小程序模拟器 -> 微信开发者工具
    pause
    exit /b 1
)

echo 正在复制小游戏配置文件...

REM 复制 game.json
if exist "%GAME_JSON_SOURCE%" (
    copy "%GAME_JSON_SOURCE%" "%SOURCE_DIR%\game.json" >nul
    echo ✓ game.json 已复制
) else (
    echo 创建 game.json...
    echo {> "%SOURCE_DIR%\game.json"
    echo   "deviceOrientation": "portrait",>> "%SOURCE_DIR%\game.json"
    echo   "showStatusBar": false,>> "%SOURCE_DIR%\game.json"
    echo   "networkTimeout": {>> "%SOURCE_DIR%\game.json"
    echo     "request": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "connectSocket": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "uploadFile": 60000,>> "%SOURCE_DIR%\game.json"
    echo     "downloadFile": 60000>> "%SOURCE_DIR%\game.json"
    echo   },>> "%SOURCE_DIR%\game.json"
    echo   "subpackages": [],>> "%SOURCE_DIR%\game.json"
    echo   "plugins": {},>> "%SOURCE_DIR%\game.json"
    echo   "preloadRule": {},>> "%SOURCE_DIR%\game.json"
    echo   "resizable": false>> "%SOURCE_DIR%\game.json"
    echo }>> "%SOURCE_DIR%\game.json"
    echo ✓ game.json 已创建
)

REM 复制 app.js 为 game.js
if exist "%SOURCE_DIR%\app.js" (
    copy "%SOURCE_DIR%\app.js" "%SOURCE_DIR%\game.js" >nul
    echo ✓ game.js 已创建
)

echo.
echo ========================================
echo 转换完成！
echo ========================================
echo.
echo 现在可以在微信开发者工具中：
echo 1. 选择"小游戏"项目类型
echo 2. 导入目录：%cd%\%SOURCE_DIR%
echo 3. 填入您的小游戏 AppID
echo.
echo 按任意键退出...
pause > nul

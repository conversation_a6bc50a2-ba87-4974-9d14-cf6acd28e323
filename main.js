// 小游戏全局兼容性补丁 - 必须在所有其他代码之前执行
(function() {
  // 获取全局对象
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  // 确保 global 对象存在
  if (typeof global === 'undefined') {
    globalObj.global = globalObj;
  }

  // 小游戏环境检测和 API 补丁
  if (typeof wx !== 'undefined') {
    console.log('应用小游戏全局兼容性补丁');

    // wx.canIUse 方法
    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = [
          'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
          'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting', 'request',
          'connectSocket', 'onSocketOpen', 'onSocketClose', 'onSocketMessage',
          'onSocketError', 'sendSocketMessage', 'closeSocket', 'showToast',
          'showModal', 'showLoading', 'hideLoading', 'setStorage', 'getStorage',
          'removeStorage', 'clearStorage', 'setStorageSync', 'getStorageSync',
          'removeStorageSync', 'clearStorageSync', 'onShow', 'onHide', 'offShow',
          'offHide', 'exitMiniProgram', 'navigateToMiniProgram', 'getUpdateManager'
        ];
        return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';
      };
    }

    // 基础 API 兼容性
    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
      wx.getAppBaseInfo = wx.getSystemInfoSync;
    }
    if (!wx.getWindowInfo && wx.getSystemInfoSync) {
      wx.getWindowInfo = wx.getSystemInfoSync;
    }
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
      wx.getDeviceInfo = wx.getSystemInfoSync;
    }
    if (!wx.getSystemSetting && wx.getSystemInfoSync) {
      wx.getSystemSetting = wx.getSystemInfoSync;
    }
    if (!wx.getAppAuthorizeSetting) {
      wx.getAppAuthorizeSetting = function() {
        return {
          albumAuthorized: 'authorized',
          bluetoothAuthorized: 'authorized',
          cameraAuthorized: 'authorized',
          locationAuthorized: 'authorized',
          locationReducedAccuracy: false,
          microphoneAuthorized: 'authorized',
          notificationAuthorized: 'authorized'
        };
      };
    }

    // 全局函数兼容性
    if (typeof Page === 'undefined') {
      globalObj.Page = function(options) {
        console.log('Page function called in mini-game environment');
        return options;
      };
    }
    if (typeof Component === 'undefined') {
      globalObj.Component = function(options) {
        console.log('Component function called in mini-game environment');
        return options;
      };
    }
    if (typeof App === 'undefined') {
      globalObj.App = function(options) {
        console.log('App function called in mini-game environment');
        return options;
      };
    }
    if (typeof getApp === 'undefined') {
      globalObj.getApp = function(options) {
        console.log('getApp function called in mini-game environment');
        return { $vm: null, globalData: {} };
      };
    }
    if (typeof getCurrentPages === 'undefined') {
      globalObj.getCurrentPages = function() {
        console.log('getCurrentPages function called in mini-game environment');
        return [];
      };
    }

    console.log('小游戏全局兼容性补丁应用完成');
  }
})();

import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif
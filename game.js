// 小游戏入口文件

// 小游戏 API 兼容性补丁 - 必须在其他模块加载前执行
if (typeof wx !== 'undefined') {
  console.log('应用小游戏 API 兼容性补丁')

  // 添加 wx.canIUse 方法
  if (!wx.canIUse) {
    wx.canIUse = function(apiName) {
      const gameAPIs = [
        'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
        'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting', 'request',
        'connectSocket', 'onSocketOpen', 'onSocketClose', 'onSocketMessage',
        'onSocketError', 'sendSocketMessage', 'closeSocket', 'showToast',
        'showModal', 'showLoading', 'hideLoading', 'setStorage', 'getStorage',
        'removeStorage', 'clearStorage', 'setStorageSync', 'getStorageSync',
        'removeStorageSync', 'clearStorageSync', 'onShow', 'onHide', 'offShow',
        'offHide', 'exitMiniProgram', 'navigateToMiniProgram', 'getUpdateManager'
      ]

      if (gameAPIs.includes(apiName)) {
        return typeof wx[apiName] === 'function'
      }
      return typeof wx[apiName] !== 'undefined'
    }
  }

  // 确保基础 API 存在
  if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
    wx.getAppBaseInfo = wx.getSystemInfoSync
  }
  if (!wx.getWindowInfo && wx.getSystemInfoSync) {
    wx.getWindowInfo = wx.getSystemInfoSync
  }
  if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
    wx.getDeviceInfo = wx.getSystemInfoSync
  }

  // 添加小游戏环境缺失的全局函数
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis
    if (typeof window !== 'undefined') return window
    if (typeof global !== 'undefined') return global
    if (typeof self !== 'undefined') return self
    return this
  })()

  if (typeof Page === 'undefined') {
    globalObj.Page = function(options) {
      console.log('Page function called in mini-game environment')
      return options
    }
  }

  if (typeof Component === 'undefined') {
    globalObj.Component = function(options) {
      console.log('Component function called in mini-game environment')
      return options
    }
  }

  if (typeof App === 'undefined') {
    globalObj.App = function(options) {
      console.log('App function called in mini-game environment')
      return options
    }
  }

  // 添加 getApp 函数
  if (typeof getApp === 'undefined') {
    globalObj.getApp = function(options) {
      console.log('getApp function called in mini-game environment')
      return {
        $vm: null,
        globalData: {}
      }
    }
  }

  // 添加 getCurrentPages 函数
  if (typeof getCurrentPages === 'undefined') {
    globalObj.getCurrentPages = function() {
      console.log('getCurrentPages function called in mini-game environment')
      return []
    }
  }

  console.log('小游戏 API 补丁应用完成')
}

// 引入uni-app框架
import './uni.promisify.adaptor'

// 小游戏适配器
if (typeof wx !== 'undefined') {
  // 微信小游戏环境
  wx.cloud && wx.cloud.init()

  // 设置小游戏环境标识
  wx.getSystemInfo({
    success: function(res) {
      console.log('小游戏系统信息:', res)
    }
  })
}

// 引入主应用
import './main.js'

// 游戏启动
console.log('仗剑江湖行小游戏启动成功')

// 小游戏入口文件

// 小游戏 API 兼容性补丁 - 必须在其他模块加载前执行
if (typeof wx !== 'undefined' && !wx.canIUse) {
  console.log('应用小游戏 API 兼容性补丁')

  wx.canIUse = function(apiName) {
    const gameAPIs = [
      'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
      'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting', 'request',
      'connectSocket', 'onSocketOpen', 'onSocketClose', 'onSocketMessage',
      'onSocketError', 'sendSocketMessage', 'closeSocket', 'showToast',
      'showModal', 'showLoading', 'hideLoading', 'setStorage', 'getStorage',
      'removeStorage', 'clearStorage', 'setStorageSync', 'getStorageSync',
      'removeStorageSync', 'clearStorageSync', 'onShow', 'onHide', 'offShow',
      'offHide', 'exitMiniProgram', 'navigateToMiniProgram', 'getUpdateManager'
    ]

    if (gameAPIs.includes(apiName)) {
      return typeof wx[apiName] === 'function'
    }
    return typeof wx[apiName] !== 'undefined'
  }

  // 确保基础 API 存在
  if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
    wx.getAppBaseInfo = wx.getSystemInfoSync
  }
  if (!wx.getWindowInfo && wx.getSystemInfoSync) {
    wx.getWindowInfo = wx.getSystemInfoSync
  }
  if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
    wx.getDeviceInfo = wx.getSystemInfoSync
  }

  console.log('小游戏 API 补丁应用完成')
}

// 引入uni-app框架
import './uni.promisify.adaptor'

// 小游戏适配器
if (typeof wx !== 'undefined') {
  // 微信小游戏环境
  wx.cloud && wx.cloud.init()

  // 设置小游戏环境标识
  wx.getSystemInfo({
    success: function(res) {
      console.log('小游戏系统信息:', res)
    }
  })
}

// 引入主应用
import './main.js'

// 游戏启动
console.log('仗剑江湖行小游戏启动成功')

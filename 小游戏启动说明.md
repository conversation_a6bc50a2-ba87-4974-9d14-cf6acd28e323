# 仗剑江湖行 - 小游戏启动说明

## 项目已成功转换为小游戏

您的项目已经从小程序成功转换为小游戏，以下是启动方法：

## 方法一：使用 HBuilderX（推荐）

1. **下载并安装 HBuilderX**
   - 官网：https://www.dcloud.io/hbuilderx.html
   - 选择正式版下载

2. **打开项目**
   - 启动 HBuilderX
   - 文件 -> 打开目录 -> 选择项目根目录：`D:\zjjhx\仗剑江湖行`

3. **编译为小游戏**
   - 点击菜单：运行 -> 运行到小程序模拟器 -> 微信开发者工具
   - 或者：发行 -> 小程序-微信（仅适用于uni-app）

4. **在微信开发者工具中打开**
   - 选择"小游戏"项目类型
   - 项目目录：`D:\zjjhx\仗剑江湖行\unpackage\dist\dev\mp-weixin`

## 方法二：直接使用微信开发者工具

1. **确保已编译**
   - 需要先通过 HBuilderX 编译项目
   - 编译后会生成：`unpackage\dist\dev\mp-weixin` 目录

2. **导入小游戏**
   - 打开微信开发者工具
   - 选择"小游戏"
   - 项目目录选择：`D:\zjjhx\仗剑江湖行\unpackage\dist\dev\mp-weixin`
   - AppID：填入您的小游戏 AppID

## 已修改的文件

1. **manifest.json** - 添加了小游戏配置
2. **package.json** - 更新了构建脚本和描述
3. **game.json** - 新建小游戏配置文件
4. **game.js** - 新建小游戏入口文件

## 注意事项

- 小游戏和小程序的主要区别在于运行环境和API
- 您的游戏功能保持不变
- 发布时需要在微信公众平台选择"小游戏"类型
- 小游戏有更好的性能和更丰富的游戏API

## 如果遇到问题

1. 确保 HBuilderX 版本是最新的
2. 确保微信开发者工具支持小游戏开发
3. 检查 AppID 是否正确配置
4. 查看控制台错误信息

## 后续发布

1. 在微信公众平台注册小游戏
2. 获取小游戏 AppID
3. 在 manifest.json 中填入正确的 AppID
4. 通过微信开发者工具上传代码
5. 在微信公众平台提交审核

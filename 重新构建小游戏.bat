@echo off
chcp 65001 >nul
echo ========================================
echo        重新构建小游戏项目
echo ========================================
echo.

echo 第一步：清理编译缓存...
if exist "unpackage" (
    rmdir /s /q "unpackage"
    echo 已清理编译缓存
) else (
    echo 编译缓存已清理
)

echo.
echo 第二步：检查源码配置...

REM 检查关键源码文件
if exist "manifest.json" (
    echo ✓ manifest.json 存在
) else (
    echo ✗ manifest.json 不存在
    pause
    exit /b 1
)

if exist "pages.json" (
    echo ✓ pages.json 存在
) else (
    echo ✗ pages.json 不存在
    pause
    exit /b 1
)

if exist "main.js" (
    echo ✓ main.js 存在
) else (
    echo ✗ main.js 不存在
    pause
    exit /b 1
)

if exist "mini-game-patch.js" (
    echo ✓ mini-game-patch.js 存在
) else (
    echo ✗ mini-game-patch.js 不存在
    pause
    exit /b 1
)

if exist "game.json" (
    echo ✓ game.json 存在
) else (
    echo ✗ game.json 不存在
    pause
    exit /b 1
)

if exist "game.js" (
    echo ✓ game.js 存在
) else (
    echo ✗ game.js 不存在
    pause
    exit /b 1
)

echo.
echo 第三步：验证源码配置...

REM 检查 manifest.json 中的小游戏配置
findstr "gamePlugin" "manifest.json" >nul
if %errorlevel% equ 0 (
    echo ✓ manifest.json 包含小游戏配置
) else (
    echo ✗ manifest.json 缺少小游戏配置
)

REM 检查 pages.json 中的首页配置
findstr "pages/index/index" "pages.json" >nul
if %errorlevel% equ 0 (
    echo ✓ pages.json 包含首页配置
) else (
    echo ✗ pages.json 缺少首页配置
)

REM 检查 main.js 中的补丁导入
findstr "mini-game-patch" "main.js" >nul
if %errorlevel% equ 0 (
    echo ✓ main.js 包含补丁导入
) else (
    echo ✗ main.js 缺少补丁导入
)

echo.
echo 第四步：等待手动编译...
echo.
echo 请按照以下步骤操作：
echo 1. 打开 HBuilderX
echo 2. 打开项目：%cd%
echo 3. 点击：运行 -> 运行到小程序模拟器 -> 微信开发者工具
echo 4. 等待编译完成
echo 5. 编译完成后，按任意键继续...
echo.
pause

echo.
echo 第五步：检查编译结果...

set "BUILD_DIR=unpackage\dist\dev\mp-weixin"

if not exist "%BUILD_DIR%" (
    echo ✗ 编译目录不存在，请先编译项目
    pause
    exit /b 1
)

echo ✓ 编译目录存在

REM 检查关键编译文件
if exist "%BUILD_DIR%\app.js" (
    echo ✓ app.js 已生成
) else (
    echo ✗ app.js 未生成
)

if exist "%BUILD_DIR%\app.json" (
    echo ✓ app.json 已生成
) else (
    echo ✗ app.json 未生成
)

if exist "%BUILD_DIR%\common\vendor.js" (
    echo ✓ vendor.js 已生成
) else (
    echo ✗ vendor.js 未生成
)

echo.
echo 第六步：应用小游戏补丁...

REM 创建 game.json
echo 创建 game.json...
echo {> "%BUILD_DIR%\game.json"
echo   "deviceOrientation": "portrait",>> "%BUILD_DIR%\game.json"
echo   "showStatusBar": false,>> "%BUILD_DIR%\game.json"
echo   "networkTimeout": {>> "%BUILD_DIR%\game.json"
echo     "request": 60000,>> "%BUILD_DIR%\game.json"
echo     "connectSocket": 60000,>> "%BUILD_DIR%\game.json"
echo     "uploadFile": 60000,>> "%BUILD_DIR%\game.json"
echo     "downloadFile": 60000>> "%BUILD_DIR%\game.json"
echo   },>> "%BUILD_DIR%\game.json"
echo   "subpackages": [],>> "%BUILD_DIR%\game.json"
echo   "plugins": {},>> "%BUILD_DIR%\game.json"
echo   "preloadRule": {},>> "%BUILD_DIR%\game.json"
echo   "resizable": false>> "%BUILD_DIR%\game.json"
echo }>> "%BUILD_DIR%\game.json"

REM 复制 app.js 为 game.js
if exist "%BUILD_DIR%\app.js" (
    copy "%BUILD_DIR%\app.js" "%BUILD_DIR%\game.js" >nul
    echo ✓ game.js 已创建
)

echo.
echo 第七步：验证最终结果...

if exist "%BUILD_DIR%\game.json" (
    echo ✓ game.json 存在
) else (
    echo ✗ game.json 不存在
)

if exist "%BUILD_DIR%\game.js" (
    echo ✓ game.js 存在
) else (
    echo ✗ game.js 不存在
)

REM 检查补丁是否包含在编译结果中
findstr "mini-game" "%BUILD_DIR%\app.js" >nul
if %errorlevel% equ 0 (
    echo ✓ 编译结果包含小游戏补丁
) else (
    echo ✗ 编译结果缺少小游戏补丁
)

echo.
echo ========================================
echo 小游戏构建完成！
echo ========================================
echo.
echo 现在可以在微信开发者工具中：
echo 1. 选择"小游戏"项目类型
echo 2. 导入目录：%cd%\%BUILD_DIR%
echo 3. 填入您的小游戏 AppID
echo 4. 查看控制台日志验证补丁是否生效
echo.
pause

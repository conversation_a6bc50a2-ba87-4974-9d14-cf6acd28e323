"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/login/login.js";
  "./pages/index/index.js";
  "./pages/character/character.js";
  "./pages/skills/skills.js";
  "./pages/shop/shop.js";
  "./pages/guild/guild.js";
  "./pages/character/backpack.js";
  "./pages/crafting/crafting.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("App Launch");
  },
  onShow: function() {
    console.log("App Show");
  },
  onHide: function() {
    console.log("App Hide");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.disconnect === "function") {
        wsManager.disconnect();
        console.log("全局 WebSocket 已断开");
      }
    } catch (e) {
      console.error("断开 WebSocket 失败:", e);
    }
  }
};
(function() {
  const globalObj = function() {
    if (typeof globalThis !== "undefined")
      return globalThis;
    if (typeof window !== "undefined")
      return window;
    if (typeof global !== "undefined")
      return global;
    if (typeof self !== "undefined")
      return self;
    return this;
  }();
  if (typeof global === "undefined") {
    globalObj.global = globalObj;
  }
  if (typeof common_vendor.wx$1 !== "undefined") {
    console.log("应用小游戏全局兼容性补丁");
    if (!common_vendor.wx$1.canIUse) {
      common_vendor.wx$1.canIUse = function(apiName) {
        const gameAPIs = [
          "getSystemInfoSync",
          "getSystemInfo",
          "getAppBaseInfo",
          "getWindowInfo",
          "getDeviceInfo",
          "getSystemSetting",
          "getAppAuthorizeSetting",
          "request",
          "connectSocket",
          "onSocketOpen",
          "onSocketClose",
          "onSocketMessage",
          "onSocketError",
          "sendSocketMessage",
          "closeSocket",
          "showToast",
          "showModal",
          "showLoading",
          "hideLoading",
          "setStorage",
          "getStorage",
          "removeStorage",
          "clearStorage",
          "setStorageSync",
          "getStorageSync",
          "removeStorageSync",
          "clearStorageSync",
          "onShow",
          "onHide",
          "offShow",
          "offHide",
          "exitMiniProgram",
          "navigateToMiniProgram",
          "getUpdateManager"
        ];
        return gameAPIs.includes(apiName) ? typeof common_vendor.wx$1[apiName] === "function" : typeof common_vendor.wx$1[apiName] !== "undefined";
      };
    }
    if (!common_vendor.wx$1.getAppBaseInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getAppBaseInfo = common_vendor.wx$1.getSystemInfoSync;
    }
    if (!common_vendor.wx$1.getWindowInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getWindowInfo = common_vendor.wx$1.getSystemInfoSync;
    }
    if (!common_vendor.wx$1.getDeviceInfo && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getDeviceInfo = common_vendor.wx$1.getSystemInfoSync;
    }
    if (!common_vendor.wx$1.getSystemSetting && common_vendor.wx$1.getSystemInfoSync) {
      common_vendor.wx$1.getSystemSetting = common_vendor.wx$1.getSystemInfoSync;
    }
    if (!common_vendor.wx$1.getAppAuthorizeSetting) {
      common_vendor.wx$1.getAppAuthorizeSetting = function() {
        return {
          albumAuthorized: "authorized",
          bluetoothAuthorized: "authorized",
          cameraAuthorized: "authorized",
          locationAuthorized: "authorized",
          locationReducedAccuracy: false,
          microphoneAuthorized: "authorized",
          notificationAuthorized: "authorized"
        };
      };
    }
    if (typeof Page === "undefined") {
      globalObj.Page = function(options) {
        console.log("Page function called in mini-game environment");
        return options;
      };
    }
    if (typeof Component === "undefined") {
      globalObj.Component = function(options) {
        console.log("Component function called in mini-game environment");
        return options;
      };
    }
    if (typeof _sfc_main === "undefined") {
      globalObj.App = function(options) {
        console.log("App function called in mini-game environment");
        return options;
      };
    }
    if (typeof getApp === "undefined") {
      globalObj.getApp = function(options) {
        console.log("getApp function called in mini-game environment");
        return { $vm: null, globalData: {} };
      };
    }
    if (typeof getCurrentPages === "undefined") {
      globalObj.getCurrentPages = function() {
        console.log("getCurrentPages function called in mini-game environment");
        return [];
      };
    }
    console.log("小游戏全局兼容性补丁应用完成");
  }
})();
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;

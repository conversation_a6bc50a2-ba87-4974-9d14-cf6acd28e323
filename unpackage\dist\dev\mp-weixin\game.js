"use strict";

// Mini-game compatibility patch - must execute before vendor.js
(function() {
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  if (typeof global === 'undefined') {
    globalObj.global = globalObj;
  }

  if (typeof wx !== 'undefined') {
    console.log('Applying mini-game compatibility patch');

    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = ['getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo', 'getDeviceInfo'];
        return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';
      };
    }

    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) wx.getAppBaseInfo = wx.getSystemInfoSync;
    if (!wx.getWindowInfo && wx.getSystemInfoSync) wx.getWindowInfo = wx.getSystemInfoSync;
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) wx.getDeviceInfo = wx.getSystemInfoSync;

    if (typeof Page === 'undefined') globalObj.Page = function(options) { return options; };
    if (typeof Component === 'undefined') globalObj.Component = function(options) { return options; };
    if (typeof App === 'undefined') globalObj.App = function(options) { return options; };
    if (typeof getApp === 'undefined') globalObj.getApp = function() { return {$vm:null,globalData:{}}; };

    console.log('Mini-game compatibility patch applied successfully');
  }
})();

Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/login/login.js";
  "./pages/index/index.js";
  "./pages/character/character.js";
  "./pages/skills/skills.js";
  "./pages/shop/shop.js";
  "./pages/guild/guild.js";
  "./pages/character/backpack.js";
  "./pages/crafting/crafting.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("Mini-Game App Launch");
    // 小游戏启动逻辑
    try {
      // 初始化游戏数据
      const gameData = require("./utils/gameData.js");
      console.log("Game data initialized");

      // 初始化 WebSocket 连接
      const wsManager = require("./utils/websocket.js");
      if (wsManager && typeof wsManager.connect === "function") {
        wsManager.connect();
        console.log("WebSocket 连接已建立");
      }
    } catch (e) {
      console.error("初始化失败:", e);
    }
  },
  onShow: function() {
    console.log("Mini-Game App Show");
  },
  onHide: function() {
    console.log("Mini-Game App Hide");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.disconnect === "function") {
        wsManager.disconnect();
        console.log("全局 WebSocket 已断开");
      }
    } catch (e) {
      console.error("断开 WebSocket 失败:", e);
    }
  }
};

// 小游戏环境下的应用创建
function createApp() {
  console.log("Creating mini-game app");
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}

// 小游戏启动
try {
  const appInstance = createApp();
  if (appInstance && appInstance.app) {
    // 在小游戏环境中，可能不需要挂载到 DOM
    console.log("Mini-game app created successfully");

    // 直接调用 onLaunch
    if (_sfc_main.onLaunch) {
      _sfc_main.onLaunch();
    }
  }
} catch (e) {
  console.error("Mini-game app creation failed:", e);
}

exports.createApp = createApp;

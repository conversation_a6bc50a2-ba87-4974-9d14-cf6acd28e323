"use strict";

// Mini-game compatibility patch - must execute before vendor.js
(function() {
  const globalObj = (function() {
    if (typeof globalThis !== 'undefined') return globalThis;
    if (typeof window !== 'undefined') return window;
    if (typeof global !== 'undefined') return global;
    if (typeof self !== 'undefined') return self;
    return this;
  })();

  if (typeof global === 'undefined') {
    globalObj.global = globalObj;
  }

  if (typeof wx !== 'undefined') {
    console.log('Applying mini-game compatibility patch');

    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = ['getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo', 'getDeviceInfo'];
        return gameAPIs.includes(apiName) ? typeof wx[apiName] === 'function' : typeof wx[apiName] !== 'undefined';
      };
    }

    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) wx.getAppBaseInfo = wx.getSystemInfoSync;
    if (!wx.getWindowInfo && wx.getSystemInfoSync) wx.getWindowInfo = wx.getSystemInfoSync;
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) wx.getDeviceInfo = wx.getSystemInfoSync;

    if (typeof Page === 'undefined') globalObj.Page = function(options) { return options; };
    if (typeof Component === 'undefined') globalObj.Component = function(options) { return options; };
    if (typeof App === 'undefined') globalObj.App = function(options) { return options; };
    if (typeof getApp === 'undefined') globalObj.getApp = function() { return {$vm:null,globalData:{}}; };

    console.log('Mini-game compatibility patch applied successfully');
  }
})();

Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" }
const common_vendor = require("./common/vendor.js");
// Add your original app.js content here

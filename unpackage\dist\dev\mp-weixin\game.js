"use strict";

// 小游戏 API 兼容性补丁 - 必须在其他模块加载前执行
(function() {
  if (typeof wx !== 'undefined') {
    console.log('应用小游戏 API 兼容性补丁');

    // 添加 wx.canIUse 方法
    if (!wx.canIUse) {
      wx.canIUse = function(apiName) {
        const gameAPIs = [
          'getSystemInfoSync', 'getSystemInfo', 'getAppBaseInfo', 'getWindowInfo',
          'getDeviceInfo', 'getSystemSetting', 'getAppAuthorizeSetting', 'request',
          'connectSocket', 'onSocketOpen', 'onSocketClose', 'onSocketMessage',
          'onSocketError', 'sendSocketMessage', 'closeSocket', 'showToast',
          'showModal', 'showLoading', 'hideLoading', 'setStorage', 'getStorage',
          'removeStorage', 'clearStorage', 'setStorageSync', 'getStorageSync',
          'removeStorageSync', 'clearStorageSync', 'onShow', 'onHide', 'offShow',
          'offHide', 'exitMiniProgram', 'navigateToMiniProgram', 'getUpdateManager'
        ];

        if (gameAPIs.includes(apiName)) {
          return typeof wx[apiName] === 'function';
        }
        return typeof wx[apiName] !== 'undefined';
      };
    }

    // 确保基础 API 存在
    if (!wx.getAppBaseInfo && wx.getSystemInfoSync) {
      wx.getAppBaseInfo = wx.getSystemInfoSync;
    }
    if (!wx.getWindowInfo && wx.getSystemInfoSync) {
      wx.getWindowInfo = wx.getSystemInfoSync;
    }
    if (!wx.getDeviceInfo && wx.getSystemInfoSync) {
      wx.getDeviceInfo = wx.getSystemInfoSync;
    }

    // 添加小游戏环境缺失的全局函数
    if (typeof Page === 'undefined') {
      global.Page = function(options) {
        console.log('Page function called in mini-game environment');
        return options;
      };
    }

    if (typeof Component === 'undefined') {
      global.Component = function(options) {
        console.log('Component function called in mini-game environment');
        return options;
      };
    }

    if (typeof App === 'undefined') {
      global.App = function(options) {
        console.log('App function called in mini-game environment');
        return options;
      };
    }

    console.log('小游戏 API 补丁应用完成');
  }
})();

Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/login/login.js";
  "./pages/index/index.js";
  "./pages/character/character.js";
  "./pages/skills/skills.js";
  "./pages/shop/shop.js";
  "./pages/guild/guild.js";
  "./pages/character/backpack.js";
  "./pages/crafting/crafting.js";
}
const _sfc_main = {
  onLaunch: function() {
    console.log("小游戏启动");
  },
  onShow: function() {
    console.log("小游戏显示");
  },
  onHide: function() {
    console.log("小游戏隐藏");
    try {
      const wsManager = require("./utils/websocket.js").default || require("./utils/websocket.js");
      if (wsManager && typeof wsManager.disconnect === "function") {
        wsManager.disconnect();
        console.log("全局 WebSocket 已断开");
      }
    } catch (e) {
      console.error("断开 WebSocket 失败:", e);
    }
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
